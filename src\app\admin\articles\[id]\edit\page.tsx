'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { PageHeader } from '@/components/PageHeader';
import { ArrowRight, Save, Eye, Trash2 } from 'lucide-react';
import { Article, ArticleCategory, ArticleFormData } from '@/types/article';

interface EditArticlePageProps {
  params: { id: string };
}

export default function EditArticlePage({ params }: EditArticlePageProps) {
  const router = useRouter();
  const [article, setArticle] = useState<Article | null>(null);
  const [categories, setCategories] = useState<ArticleCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [formData, setFormData] = useState<ArticleFormData>({
    title: '',
    slug: '',
    description: '',
    content: '',
    category: '',
    tags: '',
    author: '',
    readTime: '',
    status: 'draft',
    featured: false,
    seoTitle: '',
    seoDescription: '',
    seoKeywords: '',
    relatedTools: '',
    relatedArticles: '',
    imageUrl: '',
    imageAlt: '',
  });

  useEffect(() => {
    loadArticle();
    loadCategories();
  }, [params.id]);

  const loadArticle = async () => {
    try {
      const response = await fetch(`/api/admin/articles/${params.id}`);
      if (response.ok) {
        const data = await response.json();
        const articleData = data.article;
        setArticle(articleData);
        
        // Populate form data
        setFormData({
          title: articleData.title || '',
          slug: articleData.slug || '',
          description: articleData.description || '',
          content: articleData.content || '',
          category: articleData.category || '',
          tags: articleData.tags?.join(', ') || '',
          author: articleData.author || '',
          readTime: articleData.readTime || '',
          status: articleData.status || 'draft',
          featured: articleData.featured || false,
          seoTitle: articleData.seoTitle || '',
          seoDescription: articleData.seoDescription || '',
          seoKeywords: articleData.seoKeywords?.join(', ') || '',
          relatedTools: articleData.relatedTools?.join(', ') || '',
          relatedArticles: articleData.relatedArticles?.join(', ') || '',
          imageUrl: articleData.imageUrl || '',
          imageAlt: articleData.imageAlt || '',
        });
      } else {
        alert('فشل في تحميل المقال');
        router.push('/admin/articles');
      }
    } catch (error) {
      console.error('Error loading article:', error);
      alert('فشل في تحميل المقال');
      router.push('/admin/articles');
    } finally {
      setLoading(false);
    }
  };

  const loadCategories = async () => {
    try {
      const response = await fetch('/api/categories');
      if (response.ok) {
        const data = await response.json();
        setCategories(data.categories || []);
      }
    } catch (error) {
      console.error('Error loading categories:', error);
    }
  };

  const handleInputChange = (field: keyof ArticleFormData, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (status: 'draft' | 'published') => {
    if (!formData.title || !formData.description || !formData.content || !formData.category) {
      alert('يرجى ملء جميع الحقول المطلوبة');
      return;
    }

    setSaving(true);
    try {
      const submitData = {
        ...formData,
        status,
        tags: formData.tags.split(',').map(tag => tag.trim()).filter(Boolean),
        seoKeywords: formData.seoKeywords.split(',').map(keyword => keyword.trim()).filter(Boolean),
        relatedTools: formData.relatedTools.split(',').map(tool => tool.trim()).filter(Boolean),
        relatedArticles: formData.relatedArticles.split(',').map(article => article.trim()).filter(Boolean),
      };

      const response = await fetch(`/api/admin/articles/${params.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData),
      });

      if (response.ok) {
        alert(status === 'published' ? 'تم نشر المقال بنجاح' : 'تم حفظ المقال كمسودة');
        router.push('/admin/articles');
      } else {
        const error = await response.json();
        alert(error.error || 'فشل في حفظ المقال');
      }
    } catch (error) {
      console.error('Error saving article:', error);
      alert('فشل في حفظ المقال');
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async () => {
    if (!confirm('هل أنت متأكد من حذف هذا المقال؟ لا يمكن التراجع عن هذا الإجراء.')) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/articles/${params.id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        alert('تم حذف المقال بنجاح');
        router.push('/admin/articles');
      } else {
        const error = await response.json();
        alert(error.error || 'فشل في حذف المقال');
      }
    } catch (error) {
      console.error('Error deleting article:', error);
      alert('فشل في حذف المقال');
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center py-12">جاري التحميل...</div>
      </div>
    );
  }

  if (!article) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center py-12">المقال غير موجود</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center gap-4">
        <Button 
          variant="outline" 
          onClick={() => router.back()}
          className="flex items-center gap-2"
        >
          <ArrowRight className="h-4 w-4" />
          العودة
        </Button>
        <PageHeader 
          title={`تحرير: ${article.title}`} 
          description="تحرير المقال"
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>المحتوى الأساسي</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="title">العنوان *</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  placeholder="عنوان المقال"
                />
              </div>

              <div>
                <Label htmlFor="slug">الرابط المختصر</Label>
                <Input
                  id="slug"
                  value={formData.slug}
                  onChange={(e) => handleInputChange('slug', e.target.value)}
                  placeholder="article-slug"
                />
              </div>

              <div>
                <Label htmlFor="description">الوصف *</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="وصف مختصر للمقال"
                  rows={3}
                />
              </div>

              <div>
                <Label htmlFor="content">المحتوى *</Label>
                <Textarea
                  id="content"
                  value={formData.content}
                  onChange={(e) => handleInputChange('content', e.target.value)}
                  placeholder="محتوى المقال بصيغة HTML"
                  rows={15}
                  className="font-mono text-sm"
                />
              </div>
            </CardContent>
          </Card>

          {/* SEO Settings */}
          <Card>
            <CardHeader>
              <CardTitle>إعدادات SEO</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="seoTitle">عنوان SEO</Label>
                <Input
                  id="seoTitle"
                  value={formData.seoTitle}
                  onChange={(e) => handleInputChange('seoTitle', e.target.value)}
                  placeholder="عنوان محسن لمحركات البحث"
                />
              </div>

              <div>
                <Label htmlFor="seoDescription">وصف SEO</Label>
                <Textarea
                  id="seoDescription"
                  value={formData.seoDescription}
                  onChange={(e) => handleInputChange('seoDescription', e.target.value)}
                  placeholder="وصف محسن لمحركات البحث"
                  rows={3}
                />
              </div>

              <div>
                <Label htmlFor="seoKeywords">كلمات مفتاحية (مفصولة بفواصل)</Label>
                <Input
                  id="seoKeywords"
                  value={formData.seoKeywords}
                  onChange={(e) => handleInputChange('seoKeywords', e.target.value)}
                  placeholder="كلمة1, كلمة2, كلمة3"
                />
              </div>
            </CardContent>
          </Card>

          {/* Related Content */}
          <Card>
            <CardHeader>
              <CardTitle>المحتوى ذو الصلة</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="relatedTools">الأدوات ذات الصلة (مفصولة بفواصل)</Label>
                <Input
                  id="relatedTools"
                  value={formData.relatedTools}
                  onChange={(e) => handleInputChange('relatedTools', e.target.value)}
                  placeholder="tool1, tool2, tool3"
                />
              </div>

              <div>
                <Label htmlFor="relatedArticles">المقالات ذات الصلة (مفصولة بفواصل)</Label>
                <Input
                  id="relatedArticles"
                  value={formData.relatedArticles}
                  onChange={(e) => handleInputChange('relatedArticles', e.target.value)}
                  placeholder="article1, article2, article3"
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Publish Settings */}
          <Card>
            <CardHeader>
              <CardTitle>إعدادات النشر</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="category">الفئة *</Label>
                <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="اختر الفئة" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.slug}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="tags">العلامات (مفصولة بفواصل)</Label>
                <Input
                  id="tags"
                  value={formData.tags}
                  onChange={(e) => handleInputChange('tags', e.target.value)}
                  placeholder="علامة1, علامة2, علامة3"
                />
              </div>

              <div>
                <Label htmlFor="author">الكاتب</Label>
                <Input
                  id="author"
                  value={formData.author}
                  onChange={(e) => handleInputChange('author', e.target.value)}
                  placeholder="اسم الكاتب"
                />
              </div>

              <div>
                <Label htmlFor="readTime">وقت القراءة</Label>
                <Input
                  id="readTime"
                  value={formData.readTime}
                  onChange={(e) => handleInputChange('readTime', e.target.value)}
                  placeholder="5 دقائق"
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="featured"
                  checked={formData.featured}
                  onCheckedChange={(checked) => handleInputChange('featured', checked)}
                />
                <Label htmlFor="featured">مقال مميز</Label>
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <Card>
            <CardContent className="pt-6">
              <div className="space-y-2">
                <Button 
                  onClick={() => handleSubmit('draft')}
                  disabled={saving}
                  variant="outline"
                  className="w-full"
                >
                  <Save className="h-4 w-4 ml-2" />
                  حفظ كمسودة
                </Button>
                
                <Button 
                  onClick={() => handleSubmit('published')}
                  disabled={saving}
                  className="w-full"
                >
                  <Eye className="h-4 w-4 ml-2" />
                  نشر المقال
                </Button>

                <Button 
                  onClick={handleDelete}
                  variant="destructive"
                  className="w-full"
                >
                  <Trash2 className="h-4 w-4 ml-2" />
                  حذف المقال
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
