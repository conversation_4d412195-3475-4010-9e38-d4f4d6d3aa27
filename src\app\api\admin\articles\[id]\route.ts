import { NextRequest, NextResponse } from 'next/server';
import {
  getArticleByIdAdmin,
  updateArticle,
  deleteArticle
} from '@/lib/articles-supabase';
import { UpdateArticleData } from '@/types/article';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const article = await getArticleByIdAdmin(params.id);
    
    if (!article) {
      return NextResponse.json(
        { error: 'المقال غير موجود' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({ article });
  } catch (error) {
    console.error('Error in GET /api/admin/articles/[id]:', error);
    return NextResponse.json(
      { error: 'فشل في جلب المقال' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    
    // Validate required fields
    const requiredFields = ['title', 'description', 'content', 'category'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { error: `الحقل ${field} مطلوب` },
          { status: 400 }
        );
      }
    }

    const updateData: UpdateArticleData = {
      title: body.title,
      slug: body.slug,
      description: body.description,
      content: body.content,
      category: body.category,
      tags: body.tags || [],
      author: body.author,
      readTime: body.readTime,
      status: body.status,
      featured: body.featured || false,
      seoTitle: body.seoTitle,
      seoDescription: body.seoDescription,
      seoKeywords: body.seoKeywords || [],
      relatedTools: body.relatedTools || [],
      relatedArticles: body.relatedArticles || [],
      imageUrl: body.imageUrl,
      imageAlt: body.imageAlt,
    };

    await updateArticle(params.id, updateData);
    
    return NextResponse.json(
      { message: 'تم تحديث المقال بنجاح' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error in PUT /api/admin/articles/[id]:', error);
    return NextResponse.json(
      { error: 'فشل في تحديث المقال' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await deleteArticle(params.id);
    
    return NextResponse.json(
      { message: 'تم حذف المقال بنجاح' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error in DELETE /api/admin/articles/[id]:', error);
    return NextResponse.json(
      { error: 'فشل في حذف المقال' },
      { status: 500 }
    );
  }
}
