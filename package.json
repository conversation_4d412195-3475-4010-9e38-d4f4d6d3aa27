{"name": "nextn", "version": "0.1.0", "private": "true", "scripts": {"dev": "next dev --turbopack -p 9003", "genkit:dev": "genkit start -- tsx src/ai/dev.ts", "genkit:watch": "genkit start -- tsx --watch src/ai/dev.ts", "build": "next build", "start": "next start", "lint": "next lint", "typecheck": "tsc --noEmit", "seo:check": "node scripts/seo-checker.js", "seo:monitor": "node scripts/quick-seo-monitor.js", "seo:monitor:continuous": "node scripts/quick-seo-monitor.js --continuous", "seo:sitemap": "node scripts/sitemap-resubmitter.js", "seo:recovery": "npm run seo:check && npm run seo:monitor && npm run seo:sitemap"}, "dependencies": {"@genkit-ai/googleai": "^1.13.0", "@genkit-ai/next": "^1.13.0", "@hookform/resolvers": "^4.1.3", "@imgly/background-removal": "^1.7.0", "@pdf-lib/fontkit": "^1.1.1", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@supabase/supabase-js": "^2.53.0", "@types/bcryptjs": "^2.4.6", "@types/jspdf": "^1.3.3", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "critters": "^0.0.23", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "embla-carousel-react": "^8.6.0", "encoding": "^0.1.13", "file-saver": "^2.0.5", "genkit": "^1.13.0", "hijri-date": "^0.2.2", "html-to-docx": "^1.8.0", "html5-qrcode": "^2.3.8", "jspdf": "^3.0.1", "jszip": "^3.10.1", "lucide-react": "^0.475.0", "next": "15.3.3", "patch-package": "^8.0.0", "pdf-lib": "^1.17.1", "pdfjs-dist": "3.11.174", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "recharts": "^2.15.1", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2"}, "devDependencies": {"@types/file-saver": "^2.0.7", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "genkit-cli": "^1.13.0", "postcss": "^8", "schema-dts": "^1.1.5", "tailwindcss": "^3.4.1", "typescript": "^5"}}