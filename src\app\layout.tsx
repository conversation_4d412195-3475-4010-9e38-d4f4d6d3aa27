
import type { Metadata } from 'next';
import './globals.css';
import { Toaster } from '@/components/ui/toaster';
import { cn } from '@/lib/utils';
import { SidebarProvider } from '@/components/ui/sidebar';
import { Header } from '@/components/Header';
import { AppSidebar } from '@/components/AppSidebar';
import { Footer } from '@/components/Footer';
import { JsonLd } from '@/components/JsonLd';
import { GoogleAnalytics } from '@/components/GoogleAnalytics';
import { AutoAds } from '@/components/AdSense';
import { CookieConsent } from '@/components/CookieConsent';
import { WebVitals } from '@/components/WebVitals';
import { ArabicLanguageOptimizer } from '@/components/ArabicLanguageOptimizer';
import { SEOMonitoring } from '@/components/SEOMonitoring';
import { CookieConsentProvider } from '@/hooks/use-cookie-consent';

const siteUrl = process.env.NEXT_PUBLIC_SITE_URL;
const gaId = process.env.NEXT_PUBLIC_GA_ID;
const adSenseClient = process.env.NEXT_PUBLIC_ADSENSE_CLIENT;
const googleSiteVerification = process.env.NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION;

export const metadata: Metadata = {
  metadataBase: siteUrl ? new URL(siteUrl) : undefined,
  title: {
    default: `أدوات بالعربي - أفضل مجموعة أدوات عربية مجانية ${new Date().getFullYear()} | محول التاريخ وحاسبة الزكاة`,
    template: '%s | أدوات بالعربي',
  },
  description: `أفضل مجموعة أدوات عربية مجانية ${new Date().getFullYear()} ✅ محول التاريخ الهجري والميلادي، حاسبة الزكاة، عداد الكلمات، محول العملات، حاسبة العمر، وأكثر من 80 أداة ذكية مصممة خصيصاً للمستخدم العربي. جرب الآن!`,
  keywords: [
    'أدوات عربية مجانية', 'محول التاريخ الهجري', 'حاسبة الزكاة الإلكترونية', 'حاسبة العمر بالهجري',
    'محول العملات العربية', 'عداد الكلمات العربي', 'أدوات النصوص المتقدمة', 'حاسبة مالية ذكية',
    'أدوات رقمية عربية', 'موقع أدوات مجاني', 'حاسبات إسلامية دقيقة', 'محولات عربية احترافية',
    'أدوات أونلاين بالعربي', 'تطبيقات حسابية عربية', 'أدوات productivity عربية', 'حاسبة BMI العربية',
    'محول QR كود عربي', 'أدوات تحويل الوحدات', 'حاسبة النسبة المئوية', 'أدوات التخطيط المالي',
    'حاسبة الضريبة العربية', 'محول الوزن والطول', 'أدوات التاريخ الهجري', 'حاسبة الحمل والولادة',
    'أدوات النص العربي المتقدمة', 'حاسبة الاستثمار', 'أدوات العملات الرقمية', 'محول الوقت العالمي',
    `أدوات ${new Date().getFullYear()}`, `حاسبات ${new Date().getFullYear()}`, 'أفضل أدوات عربية',
    'Arabic tools free', 'Hijri date converter', 'Zakat calculator arabic', 'Age calculator islamic'
  ],
  openGraph: {
    title: `أدوات بالعربي - أفضل مجموعة أدوات عربية مجانية ${new Date().getFullYear()}`,
    description: `أفضل مجموعة أدوات عربية مجانية ${new Date().getFullYear()} ✅ محول التاريخ الهجري والميلادي، حاسبة الزكاة، عداد الكلمات، محول العملات، وأكثر من 80 أداة ذكية مصممة خصيصاً للمستخدم العربي.`,
    url: siteUrl,
    siteName: 'أدوات بالعربي',
    images: [
      {
        url: '/images/og-homepage.jpg',
        width: 1200,
        height: 630,
        alt: 'أدوات بالعربي - مجموعة أدوات عربية مجانية',
      }
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: `أدوات بالعربي - أفضل مجموعة أدوات عربية مجانية ${new Date().getFullYear()}`,
    description: `أفضل مجموعة أدوات عربية مجانية ${new Date().getFullYear()} ✅ محول التاريخ الهجري، حاسبة الزكاة، عداد الكلمات، محول العملات، وأكثر من 80 أداة ذكية.`,
    images: ['/images/og-homepage.jpg'],
    creator: '@adawat_org',
  },
  alternates: {
    canonical: '/',
  },
  verification: {
    google: 'google-site-verification-code',
  },
  other: {
    'msvalidate.01': 'bing-verification-code',
    'yandex-verification': 'yandex-verification-code',
  }
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const jsonLd: any = siteUrl ? {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: 'أدوات بالعربي',
    alternateName: 'Arabic Tools',
    url: siteUrl,
    inLanguage: ['ar', 'ar-SA'],
    description: `مجموعة شاملة من الأدوات والحاسبات والمحولات باللغة العربية لتسهيل حياتك اليومية ${new Date().getFullYear()}.`,
    keywords: 'أدوات عربية, حاسبة عربية, محول التاريخ, حاسبة الزكاة, أدوات مجانية',
    potentialAction: {
      '@type': 'SearchAction',
      target: {
        '@type': 'EntryPoint',
        urlTemplate: `${siteUrl}/search?q={search_term_string}`
      },
      'query-input': 'required name=search_term_string'
    },
    publisher: {
      '@type': 'Organization',
      name: 'أدوات بالعربي',
      url: siteUrl,
      logo: {
        '@type': 'ImageObject',
        url: `${siteUrl}/logo.png`,
        width: '512',
        height: '630'
      },
      sameAs: [
        'https://twitter.com/adawat_org',
        'https://facebook.com/adawat.org'
      ]
    }
  } : null;

  return (
    <html lang="ar" dir="rtl" className="h-full" suppressHydrationWarning={true}>
      <head>
    
        {jsonLd && <JsonLd data={jsonLd} />}
        <link rel="manifest" href="/manifest.json" />
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/icon-192.png" />
        <meta name="theme-color" content="#2563eb" />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5" />
        <meta name="format-detection" content="telephone=no" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="أدوات بالعربي" />
        <meta name="application-name" content="أدوات بالعربي" />
        <meta name="msapplication-TileColor" content="#2563eb" />
        <meta name="msapplication-config" content="/browserconfig.xml" />
        <meta name="author" content="أدوات بالعربي" />
        <meta name="generator" content="Next.js" />
        <meta name="referrer" content="origin-when-cross-origin" />
        <meta httpEquiv="Content-Language" content="ar" />
        <meta httpEquiv="X-UA-Compatible" content="IE=edge" />
        <meta name="yandex-verification" content="b2a47f2778f09b51" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&family=Wafeq:wght@600&display=swap" rel="stylesheet" />
        <link rel="dns-prefetch" href="//www.google-analytics.com" />
        <link rel="dns-prefetch" href="//www.googletagmanager.com" />
        <link rel="preconnect" href="https://pagead2.googlesyndication.com" crossOrigin="anonymous" />
      </head>
      <body className={cn('font-body antialiased min-h-screen bg-background w-full h-full')} suppressHydrationWarning={true}>
        <ArabicLanguageOptimizer>
          <CookieConsentProvider>
            <SidebarProvider>
            <div className="relative flex min-h-screen w-full flex-col">
              <Header />
              <div className="flex flex-1 w-full">
                 <AppSidebar />
                 <main className="flex flex-1 flex-col w-full">
                  {children}
                  <Footer />
                </main>
              </div>
            </div>
            </SidebarProvider>
            <Toaster />
            <CookieConsent />
            {gaId && <GoogleAnalytics gaId={gaId} />}
            {gaId && <WebVitals gaId={gaId} />}
            <SEOMonitoring pageType="homepage" />
            {adSenseClient && <AutoAds adClient={adSenseClient} />}
          </CookieConsentProvider>
        </ArabicLanguageOptimizer>
      </body>
    </html>
  );
}
