import { supabase, generateSlug, Database } from './supabase';
import { Article, ArticleCategory, CreateArticleData, UpdateArticleData, ArticleStats } from '@/types/article';

// دالة للحصول على supabaseAdmin فقط في الخادم
const getSupabaseAdmin = () => {
  if (typeof window !== 'undefined') {
    throw new Error('supabaseAdmin can only be used on the server side');
  }

  const { createClient } = require('@supabase/supabase-js');
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

  if (!supabaseUrl || !supabaseServiceKey) {
    throw new Error('Missing Supabase environment variables for admin client');
  }

  return createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
};

type ArticleRow = Database['public']['Tables']['articles']['Row'];
type CategoryRow = Database['public']['Tables']['categories']['Row'];
type ArticleInsert = Database['public']['Tables']['articles']['Insert'];
type ArticleUpdate = Database['public']['Tables']['articles']['Update'];

// تحويل البيانات من Supabase إلى نوع Article
const mapArticleFromDB = (row: ArticleRow): Article => ({
  id: row.id,
  title: row.title,
  slug: row.slug,
  description: row.description,
  content: row.content,
  category: row.category_slug || '',
  tags: row.tags || [],
  author: row.author,
  authorId: row.author_id || '',
  readTime: row.read_time,
  status: row.status,
  featured: row.featured,
  views: row.views,
  likes: row.likes,
  createdAt: new Date(row.created_at),
  updatedAt: new Date(row.updated_at),
  publishedAt: row.published_at ? new Date(row.published_at) : undefined,
  seoTitle: row.seo_title || '',
  seoDescription: row.seo_description || '',
  seoKeywords: row.seo_keywords || [],
  relatedTools: row.related_tools || [],
  relatedArticles: row.related_articles || [],
  imageUrl: row.image_url || '',
  imageAlt: row.image_alt || '',
});

// تحويل البيانات من Supabase إلى نوع ArticleCategory
const mapCategoryFromDB = (row: CategoryRow): ArticleCategory => ({
  id: row.id,
  name: row.name,
  slug: row.slug,
  description: row.description || '',
  color: row.color,
  icon: row.icon || '',
  order: row.order_index,
  createdAt: new Date(row.created_at),
  updatedAt: new Date(row.updated_at),
});

// إنشاء مقال جديد
export const createArticle = async (articleData: CreateArticleData): Promise<string> => {
  try {
    const slug = articleData.slug || generateSlug(articleData.title);
    
    const insertData: ArticleInsert = {
      title: articleData.title,
      slug,
      description: articleData.description,
      content: articleData.content,
      category_slug: articleData.category,
      tags: articleData.tags || [],
      author: articleData.author || 'فريق أدوات بالعربي',
      author_id: articleData.authorId,
      read_time: articleData.readTime || '5 دقائق',
      status: articleData.status || 'draft',
      featured: articleData.featured || false,
      published_at: articleData.status === 'published' ? new Date().toISOString() : null,
      seo_title: articleData.seoTitle,
      seo_description: articleData.seoDescription,
      seo_keywords: articleData.seoKeywords || [],
      related_tools: articleData.relatedTools || [],
      related_articles: articleData.relatedArticles || [],
      image_url: articleData.imageUrl,
      image_alt: articleData.imageAlt,
    };

    const supabaseAdmin = getSupabaseAdmin();
    const { data, error } = await supabaseAdmin
      .from('articles')
      .insert(insertData)
      .select('id')
      .single();

    if (error) {
      console.error('Error creating article:', error);
      throw new Error(`فشل في إنشاء المقال: ${error.message}`);
    }

    return data.id;
  } catch (error) {
    console.error('Error in createArticle:', error);
    throw error;
  }
};

// جلب مقال بواسطة ID (للمستخدمين العاديين - المقالات المنشورة فقط)
export const getArticleById = async (id: string): Promise<Article | null> => {
  try {
    const { data, error } = await supabase
      .from('articles')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // المقال غير موجود
      }
      console.error('Error fetching article:', error);
      throw new Error(`فشل في جلب المقال: ${error.message}`);
    }

    return mapArticleFromDB(data);
  } catch (error) {
    console.error('Error in getArticleById:', error);
    throw error;
  }
};

// جلب مقال بواسطة ID للأدمين (جميع المقالات بما في ذلك المسودات)
export const getArticleByIdAdmin = async (id: string): Promise<Article | null> => {
  try {
    const supabaseAdmin = getSupabaseAdmin();
    const { data, error } = await supabaseAdmin
      .from('articles')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // المقال غير موجود
      }
      console.error('Error fetching article for admin:', error);
      throw new Error(`فشل في جلب المقال: ${error.message}`);
    }

    return mapArticleFromDB(data);
  } catch (error) {
    console.error('Error in getArticleByIdAdmin:', error);
    throw error;
  }
};

// جلب مقال بواسطة Slug
export const getArticleBySlug = async (slug: string): Promise<Article | null> => {
  try {
    const { data, error } = await supabase
      .from('articles')
      .select('*')
      .eq('slug', slug)
      .eq('status', 'published')
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // المقال غير موجود
      }
      console.error('Error fetching article by slug:', error);
      throw new Error(`فشل في جلب المقال: ${error.message}`);
    }

    return mapArticleFromDB(data);
  } catch (error) {
    console.error('Error in getArticleBySlug:', error);
    throw error;
  }
};

// جلب قائمة المقالات مع فلترة (للمستخدمين العاديين - المقالات المنشورة فقط)
export const getArticles = async (options: {
  status?: string;
  category?: string;
  featured?: boolean;
  limit?: number;
  offset?: number;
  search?: string;
} = {}): Promise<{ articles: Article[]; total: number }> => {
  try {
    let query = supabase.from('articles').select('*', { count: 'exact' });

    // تطبيق الفلاتر
    if (options.status) {
      query = query.eq('status', options.status);
    }

    if (options.category) {
      query = query.eq('category_slug', options.category);
    }

    if (options.featured !== undefined) {
      query = query.eq('featured', options.featured);
    }

    if (options.search) {
      query = query.or(`title.ilike.%${options.search}%,description.ilike.%${options.search}%`);
    }

    // ترتيب وتحديد العدد
    query = query.order('created_at', { ascending: false });

    if (options.limit) {
      query = query.limit(options.limit);
    }

    if (options.offset) {
      query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
    }

    const { data, error, count } = await query;

    if (error) {
      console.error('Error fetching articles:', error);
      throw new Error(`فشل في جلب المقالات: ${error.message}`);
    }

    const articles = data?.map(mapArticleFromDB) || [];

    return {
      articles,
      total: count || 0
    };
  } catch (error) {
    console.error('Error in getArticles:', error);
    throw error;
  }
};

// جلب قائمة المقالات للأدمين (جميع المقالات بما في ذلك المسودات)
export const getArticlesAdmin = async (options: {
  status?: string;
  category?: string;
  featured?: boolean;
  limit?: number;
  offset?: number;
  search?: string;
} = {}): Promise<{ articles: Article[]; total: number }> => {
  try {
    const supabaseAdmin = getSupabaseAdmin();
    let query = supabaseAdmin.from('articles').select('*', { count: 'exact' });

    // تطبيق الفلاتر
    if (options.status) {
      query = query.eq('status', options.status);
    }

    if (options.category) {
      query = query.eq('category_slug', options.category);
    }

    if (options.featured !== undefined) {
      query = query.eq('featured', options.featured);
    }

    if (options.search) {
      query = query.or(`title.ilike.%${options.search}%,description.ilike.%${options.search}%`);
    }

    // ترتيب وتحديد العدد
    query = query.order('created_at', { ascending: false });

    if (options.limit) {
      query = query.limit(options.limit);
    }

    if (options.offset) {
      query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
    }

    const { data, error, count } = await query;

    if (error) {
      console.error('Error fetching articles for admin:', error);
      throw new Error(`فشل في جلب المقالات: ${error.message}`);
    }

    const articles = data?.map(mapArticleFromDB) || [];

    return {
      articles,
      total: count || 0
    };
  } catch (error) {
    console.error('Error in getArticlesAdmin:', error);
    throw error;
  }
};

// تحديث مقال
export const updateArticle = async (id: string, updateData: UpdateArticleData): Promise<void> => {
  try {
    const updatePayload: ArticleUpdate = {
      title: updateData.title,
      slug: updateData.slug,
      description: updateData.description,
      content: updateData.content,
      category_slug: updateData.category,
      tags: updateData.tags,
      author: updateData.author,
      read_time: updateData.readTime,
      status: updateData.status,
      featured: updateData.featured,
      seo_title: updateData.seoTitle,
      seo_description: updateData.seoDescription,
      seo_keywords: updateData.seoKeywords,
      related_tools: updateData.relatedTools,
      related_articles: updateData.relatedArticles,
      image_url: updateData.imageUrl,
      image_alt: updateData.imageAlt,
    };

    // إذا تم تغيير الحالة إلى منشور، حدث تاريخ النشر
    if (updateData.status === 'published') {
      updatePayload.published_at = new Date().toISOString();
    }

    const supabaseAdmin = getSupabaseAdmin();
    const { error } = await supabaseAdmin
      .from('articles')
      .update(updatePayload)
      .eq('id', id);

    if (error) {
      console.error('Error updating article:', error);
      throw new Error(`فشل في تحديث المقال: ${error.message}`);
    }
  } catch (error) {
    console.error('Error in updateArticle:', error);
    throw error;
  }
};

// حذف مقال
export const deleteArticle = async (id: string): Promise<void> => {
  try {
    const supabaseAdmin = getSupabaseAdmin();
    const { error } = await supabaseAdmin
      .from('articles')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting article:', error);
      throw new Error(`فشل في حذف المقال: ${error.message}`);
    }
  } catch (error) {
    console.error('Error in deleteArticle:', error);
    throw error;
  }
};

// زيادة عدد المشاهدات
export const incrementArticleViews = async (id: string): Promise<void> => {
  try {
    const { error } = await supabase.rpc('increment_views', { article_id: id });

    if (error) {
      console.error('Error incrementing views:', error);
      // لا نرمي خطأ هنا لأن هذا ليس حرجاً
    }
  } catch (error) {
    console.error('Error in incrementArticleViews:', error);
    // لا نرمي خطأ هنا لأن هذا ليس حرجاً
  }
};

// زيادة عدد الإعجابات
export const incrementArticleLikes = async (id: string): Promise<void> => {
  try {
    const { error } = await supabase.rpc('increment_likes', { article_id: id });

    if (error) {
      console.error('Error incrementing likes:', error);
      throw new Error(`فشل في إضافة الإعجاب: ${error.message}`);
    }
  } catch (error) {
    console.error('Error in incrementArticleLikes:', error);
    throw error;
  }
};

// جلب المقالات ذات الصلة
export const getRelatedArticles = async (
  currentArticleId: string,
  category: string,
  tags: string[],
  limit: number = 3
): Promise<Article[]> => {
  try {
    let query = supabase
      .from('articles')
      .select('*')
      .eq('status', 'published')
      .neq('id', currentArticleId);

    // البحث بالفئة أولاً
    if (category) {
      query = query.eq('category_slug', category);
    }

    const { data, error } = await query
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching related articles:', error);
      return [];
    }

    return data?.map(mapArticleFromDB) || [];
  } catch (error) {
    console.error('Error in getRelatedArticles:', error);
    return [];
  }
};

// جلب إحصائيات المقالات
export const getArticleStats = async (): Promise<ArticleStats> => {
  try {
    const supabaseAdmin = getSupabaseAdmin();
    const { data: articles, error } = await supabaseAdmin
      .from('articles')
      .select('status, views, likes, category_slug, tags');

    if (error) {
      console.error('Error fetching article stats:', error);
      throw new Error(`فشل في جلب الإحصائيات: ${error.message}`);
    }

    const stats = {
      totalArticles: articles?.length || 0,
      publishedArticles: articles?.filter(a => a.status === 'published').length || 0,
      draftArticles: articles?.filter(a => a.status === 'draft').length || 0,
      totalViews: articles?.reduce((sum, a) => sum + (a.views || 0), 0) || 0,
      totalLikes: articles?.reduce((sum, a) => sum + (a.likes || 0), 0) || 0,
      categoriesCount: new Set(articles?.map(a => a.category_slug).filter(Boolean)).size,
      tagsCount: new Set(articles?.flatMap(a => a.tags || [])).size,
    };

    return stats;
  } catch (error) {
    console.error('Error in getArticleStats:', error);
    throw error;
  }
};

// جلب الفئات
export const getCategories = async (): Promise<ArticleCategory[]> => {
  try {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .order('order_index', { ascending: true });

    if (error) {
      console.error('Error fetching categories:', error);
      throw new Error(`فشل في جلب الفئات: ${error.message}`);
    }

    return data?.map(mapCategoryFromDB) || [];
  } catch (error) {
    console.error('Error in getCategories:', error);
    throw error;
  }
};

// إنشاء فئة جديدة
export const createCategory = async (categoryData: {
  name: string;
  slug?: string;
  description?: string;
  color?: string;
  icon?: string;
}): Promise<string> => {
  try {
    const slug = categoryData.slug || generateSlug(categoryData.name);
    
    const supabaseAdmin = getSupabaseAdmin();
    const { data, error } = await supabaseAdmin
      .from('categories')
      .insert({
        name: categoryData.name,
        slug,
        description: categoryData.description || '',
        color: categoryData.color || '#3B82F6',
        icon: categoryData.icon,
        order_index: 0,
      })
      .select('id')
      .single();

    if (error) {
      console.error('Error creating category:', error);
      throw new Error(`فشل في إنشاء الفئة: ${error.message}`);
    }

    return data.id;
  } catch (error) {
    console.error('Error in createCategory:', error);
    throw error;
  }
};
