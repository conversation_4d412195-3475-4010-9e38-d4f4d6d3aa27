import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

// Client للاستخدام في المتصفح
export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey);

// أنواع البيانات
export interface Database {
  public: {
    Tables: {
      categories: {
        Row: {
          id: string;
          name: string;
          slug: string;
          description: string | null;
          color: string;
          icon: string | null;
          order_index: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          slug: string;
          description?: string | null;
          color?: string;
          icon?: string | null;
          order_index?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          slug?: string;
          description?: string | null;
          color?: string;
          icon?: string | null;
          order_index?: number;
          created_at?: string;
          updated_at?: string;
        };
      };
      articles: {
        Row: {
          id: string;
          title: string;
          slug: string;
          description: string;
          content: string;
          category_id: string | null;
          category_slug: string | null;
          tags: string[];
          author: string;
          author_id: string | null;
          read_time: string;
          status: 'draft' | 'published' | 'archived';
          featured: boolean;
          views: number;
          likes: number;
          published_at: string | null;
          created_at: string;
          updated_at: string;
          seo_title: string | null;
          seo_description: string | null;
          seo_keywords: string[];
          related_tools: string[];
          related_articles: string[];
          image_url: string | null;
          image_alt: string | null;
        };
        Insert: {
          id?: string;
          title: string;
          slug: string;
          description: string;
          content: string;
          category_id?: string | null;
          category_slug?: string | null;
          tags?: string[];
          author?: string;
          author_id?: string | null;
          read_time?: string;
          status?: 'draft' | 'published' | 'archived';
          featured?: boolean;
          views?: number;
          likes?: number;
          published_at?: string | null;
          created_at?: string;
          updated_at?: string;
          seo_title?: string | null;
          seo_description?: string | null;
          seo_keywords?: string[];
          related_tools?: string[];
          related_articles?: string[];
          image_url?: string | null;
          image_alt?: string | null;
        };
        Update: {
          id?: string;
          title?: string;
          slug?: string;
          description?: string;
          content?: string;
          category_id?: string | null;
          category_slug?: string | null;
          tags?: string[];
          author?: string;
          author_id?: string | null;
          read_time?: string;
          status?: 'draft' | 'published' | 'archived';
          featured?: boolean;
          views?: number;
          likes?: number;
          published_at?: string | null;
          created_at?: string;
          updated_at?: string;
          seo_title?: string | null;
          seo_description?: string | null;
          seo_keywords?: string[];
          related_tools?: string[];
          related_articles?: string[];
          image_url?: string | null;
          image_alt?: string | null;
        };
      };
      tools: {
        Row: {
          id: string;
          name: string;
          slug: string;
          description: string | null;
          category: string;
          path: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          slug: string;
          description?: string | null;
          category: string;
          path: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          slug?: string;
          description?: string | null;
          category?: string;
          path?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      tool_ratings: {
        Row: {
          id: string;
          tool_slug: string;
          rating: number;
          comment: string | null;
          user_session: string;
          user_ip: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          tool_slug: string;
          rating: number;
          comment?: string | null;
          user_session: string;
          user_ip?: string | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          tool_slug?: string;
          rating?: number;
          comment?: string | null;
          user_session?: string;
          user_ip?: string | null;
          created_at?: string;
        };
      };
      tool_rating_stats: {
        Row: {
          tool_slug: string;
          total_ratings: number;
          average_rating: number;
          rating_1_count: number;
          rating_2_count: number;
          rating_3_count: number;
          rating_4_count: number;
          rating_5_count: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          tool_slug: string;
          total_ratings?: number;
          average_rating?: number;
          rating_1_count?: number;
          rating_2_count?: number;
          rating_3_count?: number;
          rating_4_count?: number;
          rating_5_count?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          tool_slug?: string;
          total_ratings?: number;
          average_rating?: number;
          rating_1_count?: number;
          rating_2_count?: number;
          rating_3_count?: number;
          rating_4_count?: number;
          rating_5_count?: number;
          created_at?: string;
          updated_at?: string;
        };
      };
    };
  };
}

// Helper functions
export const generateSlug = (text: string): string => {
  // خريطة تحويل الأحرف العربية إلى إنجليزية
  const arabicToEnglish: { [key: string]: string } = {
    'ا': 'a', 'أ': 'a', 'إ': 'i', 'آ': 'aa',
    'ب': 'b', 'ت': 't', 'ث': 'th', 'ج': 'j',
    'ح': 'h', 'خ': 'kh', 'د': 'd', 'ذ': 'dh',
    'ر': 'r', 'ز': 'z', 'س': 's', 'ش': 'sh',
    'ص': 's', 'ض': 'd', 'ط': 't', 'ظ': 'z',
    'ع': 'a', 'غ': 'gh', 'ف': 'f', 'ق': 'q',
    'ك': 'k', 'ل': 'l', 'م': 'm', 'ن': 'n',
    'ه': 'h', 'و': 'w', 'ي': 'y', 'ى': 'a',
    'ة': 'h', 'ء': 'a'
  };

  return text
    .toLowerCase()
    // تحويل الأحرف العربية إلى إنجليزية
    .split('')
    .map(char => arabicToEnglish[char] || char)
    .join('')
    // إزالة الأحرف غير المرغوبة
    .replace(/[^a-z0-9\s-]/g, '')
    // تحويل المسافات إلى شرطات
    .replace(/\s+/g, '-')
    // إزالة الشرطات المتتالية
    .replace(/-+/g, '-')
    // إزالة الشرطات من البداية والنهاية
    .replace(/^-+|-+$/g, '')
    .trim();
};

export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('ar-SA-u-nu-latn');
};

export const formatRelativeTime = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
  
  if (diffInSeconds < 60) {
    return 'منذ لحظات';
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `منذ ${minutes} دقيقة`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `منذ ${hours} ساعة`;
  } else if (diffInSeconds < 2592000) {
    const days = Math.floor(diffInSeconds / 86400);
    return `منذ ${days} يوم`;
  } else if (diffInSeconds < 31536000) {
    const months = Math.floor(diffInSeconds / 2592000);
    return `منذ ${months} شهر`;
  } else {
    const years = Math.floor(diffInSeconds / 31536000);
    return `منذ ${years} سنة`;
  }
};
